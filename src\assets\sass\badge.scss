#badge-meaning {
  padding: var(--section-padding);
  background-color: #fff;

  .cs-container {
    width: 100%;
    max-width: var(--container-width);
    margin: auto;
    padding: 0 1rem;
  }

  .cs-content {
    max-width: 1000px;
    margin: auto;
    text-align: center;
  }

  .badge-display {
    margin: 2rem auto;
    padding: 1rem;
    max-width: 300px;

    .badge-image {
      width: 100%;
      height: auto;
      filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .cs-flex-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin-top: 1rem;
    margin-bottom: 5rem;
  }

  .quality-item {
    flex: 1;
    min-width: 280px;
    max-width: 400px;
    padding: 2rem;
    background-color: var(--primary-light);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    h3 {
      color: var(--primary);
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    p {
      color: var(--text);
      line-height: 1.6;
    }
  }
}

#badge-implementation {
  padding: var(--section-padding);
  background-color: var(--primary-light);
  margin-bottom: 5rem;

  .cs-container {
    width: 100%;
    max-width: var(--container-width);
    margin: auto;
    padding: 0 1rem;
  }

  .cs-content {
    max-width: 800px;
    margin: auto;
  }

  .implementation-steps {
    margin-top: 3rem;

    h3 {
      color: var(--primary);
      margin: 2rem 0 1rem;
      font-size: 1.3rem;
    }

    ul {
      list-style: disc;
      margin-left: 1.5rem;
      margin-top: 1rem;

      li {
        margin-bottom: 0.5rem;
        color: var(--text);
      }
    }
  }

  .code-block {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    position: relative;

    pre {
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;

      code {
        font-family: monospace;
        color: #333;
        line-height: 1.6;
      }
    }

    .copy-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
      padding: 0.5rem 1rem;
      background-color: var(--primary);
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: var(--primary-dark);
        color: #333;
      }
    }
  }
}

#badge-benefits {
  padding: var(--section-padding);
  background-color: #fff;
  margin-bottom: 5rem;

  .cs-container {
    width: 100%;
    max-width: var(--container-width);
    margin: auto;
    padding: 0 1rem;
  }

  .cs-content {
    max-width: 1000px;
    margin: auto;
    text-align: center;
  }

  .cs-topper {
    font-size: 0.9rem;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 0.1em;
    color: var(--primary);
    display: block;
    margin-bottom: 0.5rem;
    text-align: center;
  }

  .cs-badge-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    text-align: center;
  }

  .cs-text {
    margin-bottom: 2rem;
    line-height: 1.6;
    color: var(--text);
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .benefit-item {
    padding: 1.5rem;
    background-color: var(--primary-light);
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }

    h3 {
      color: var(--primary);
      margin-bottom: 1rem;
      font-size: 1.3rem;
    }

    p {
      color: var(--text);
      line-height: 1.6;
    }
  }

  .search-console-evidence {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);

    h3 {
      color: var(--primary);
      margin-bottom: 1rem;
      font-size: 1.5rem;
      text-align: center;
    }

    p {
      color: var(--text);
      line-height: 1.6;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .screenshot-container {
      width: 100%;
      max-width: 900px;
      margin: 0 auto 1.5rem;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      background-color: white;
      padding: 1rem;

      /* For the screenshot that is twice as wide as it is tall */
      aspect-ratio: 2 / 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    #search-console-img {
      width: 100%;
      height: auto;
      max-height: 100%;
      object-fit: contain;
      display: block;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.02);
      }
    }

    .evidence-caption {
      font-size: 0.9rem;
      color: #777;
      font-style: italic;
      text-align: center;
      margin-top: 0.5rem;
    }
  }
}

/* Responsive Design */
@media only screen and (max-width: 768px) {
  #badge-meaning {
    .cs-flex-group {
      flex-direction: column;
      align-items: center;
    }

    .quality-item {
      width: 100%;
    }
  }

  #badge-implementation {
    .code-block {
      padding: 1rem;

      .copy-button {
        position: static;
        display: block;
        width: 100%;
        margin-top: 1rem;
      }
    }
  }

  #badge-benefits {
    .benefits-grid {
      grid-template-columns: 1fr;
    }

    .search-console-evidence {
      padding: 1.5rem 1rem;

      h3 {
        font-size: 1.3rem;
      }

      .screenshot-container {
        padding: 0.5rem;
      }

      .evidence-caption {
        font-size: 0.8rem;
      }
    }
  }
}