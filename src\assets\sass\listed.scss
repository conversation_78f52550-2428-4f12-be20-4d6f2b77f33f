/* ============================================ */
/* Variable Definitions             */
/* ============================================ */
$primary-color: var(--primary); // Example primary color - CHANGE AS NEEDED
$text-color-primary: #333;
$text-color-secondary: #555;
$text-color-light: #777;
$border-color-light: #eee;
$border-color-medium: #e0e0e0;
$border-color-dark: #ddd;
$card-bg: #fff;
$star-color-filled: #f8ce0b; // Gold star color
$star-color-empty: #e0e0e0; // Empty star color
$success-color: #28a745; // Green for checkmarks etc.

$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1100px; // Max width for container

/* ============================================ */
/* Business Listing Styles           */
/* ============================================ */

.business-listing-container {
  max-width: $breakpoint-xl;
  margin: 2rem auto;
  padding: 0 1rem;
}

/* Business Header */
.business-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid $border-color-light;

  .header-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;

    @media (min-width: $breakpoint-sm) {
      flex-direction: row;
      align-items: center;
    }
  }

  .logo-container {
    flex-shrink: 0;
    margin-bottom: 0.5rem;

    @media (min-width: $breakpoint-sm) {
      margin-bottom: 0;
    }
  }

  .business-logo {
    display: block;
    max-width: 100px;
    height: auto;
    border-radius: 4px;
    border: 1px solid $border-color-light;

    @media (min-width: $breakpoint-sm) {
      max-width: 120px;
    }
  }

  .info-container {
    flex-grow: 1;
  }

  .business-title {
    margin: 0 0 0.25rem 0;
    font-size: 1.8rem;
    font-weight: bold;
    color: $text-color-primary;
  }

  .rating-reviews {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
  }

  .rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;

    .stars {
      font-size: 1.1rem;
      // Example using direct child color (assuming HTML has spans with inline styles or specific classes)
      // If using classes like .star-filled, .star-empty:
      // .star-filled { color: $star-color-filled; }
      // .star-empty { color: $star-color-empty; }
    }

    .rating-text {
      font-weight: bold;
      color: $text-color-secondary;
      font-size: 0.95rem;
    }
  }

  .review-count {
    margin: 0;
    font-size: 0.95rem;
    color: $text-color-secondary;
  }

  .category {
    margin: 0.5rem 0 0 0;
    font-size: 0.95rem;
    color: $text-color-secondary;
  }
}

/* Business Meta / Quick Contact */
.business-meta {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start; // Changed from space-between
  align-items: center;
  margin-bottom: 1.5rem;
  // Removed border-bottom from original .business-meta definition
  // border-bottom: 1px solid $border-color-light;
  padding-top: 0; // Added

  .contact-info-quick {
    display: flex;
    gap: 1.5rem;
    align-items: center;
    margin-top: 0.5rem; // Added margin

    a {
      text-decoration: none;
      color: $primary-color;
      font-weight: bold;
      display: inline-flex;
      align-items: center;
      gap: 0.4rem;

      &:hover {
        text-decoration: underline;
      }

      .fas {
        // Assuming Font Awesome
        font-size: 1rem;
        color: $text-color-secondary;
      }
    }
  }
}


/* Main Content Layout (2 columns on wider screens) */
.main-content-layout {
  display: grid;
  grid-template-columns: 1fr; // Default: single column
  gap: 2rem;

  @media (min-width: $breakpoint-md) {
    grid-template-columns: 2fr 1fr;
  }

  @media (min-width: $breakpoint-lg) {
    // Optional: Adjust ratio
    // grid-template-columns: 3fr 1fr;
  }
}

/* Card Style for Sections */
.card {
  background-color: $card-bg;
  border: 1px solid $border-color-medium;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  // Applied to all H2s within cards unless overridden
  h2 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    color: $text-color-primary;
    border-bottom: 1px solid $border-color-light;
    padding-bottom: 0.5rem;
  }
}

/* Left Column Content */
.content-left {
  // Section-specific styles below
}

/* Location & Hours Section */
.location-hours-section {
  .location-hours-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    @media (min-width: $breakpoint-md) {
      flex-direction: row;
      align-items: flex-start;
    }
  }

  .map-container {
    position: relative;
    height: 0;
    overflow: hidden;
    border-radius: 4px;
    border: 1px solid $border-color-dark;
    padding-bottom: 56.25%; // 16:9 Aspect Ratio default

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border: 0;
    }

    @media (min-width: $breakpoint-md) {
      flex: 1 1 40%;
      height: 250px; // Fixed height on larger screens
      padding-bottom: 0; // Remove aspect ratio padding
    }
  }

  .address-hours-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;

    @media (min-width: $breakpoint-md) {
      flex: 1 1 55%;
    }
  }

  .address {
    p {
      margin: 0 0 0.5rem 0;
      line-height: 1.5;
    }

    .directions-link {
      color: $primary-color;
      text-decoration: none;
      font-weight: bold;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .hours {
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      font-size: 0.95rem;
      color: $text-color-primary;
    }

    li {
      margin-bottom: 0.5rem;
      display: flex;
      justify-content: space-between;
      padding-bottom: 0.5rem;
      border-bottom: 1px dotted $border-color-light;

      &:last-child {
        border-bottom: none;
      }

      .time {
        font-weight: 500;
        color: $text-color-secondary;
        text-align: right;
      }
    }

    .hours-note {
      font-style: italic;
      color: $text-color-light;
      font-size: 0.9rem;
      display: block;
      margin-top: 0.5rem;
    }
  }
}

/* About Section */
.about-section {
  p {
    line-height: 1.6;
    color: $text-color-secondary;
    margin-bottom: 1em;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* Right Column / Sidebar Content */
.content-right {
  // Section-specific styles below
}

/* Highlights & Service Area Sections */
.highlights-section,
.service-area-section {
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    margin-bottom: 0.75rem;
    color: $text-color-secondary;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    gap: 0.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Highlight icon styling (using Font Awesome class)
  .fas.fa-check {
    color: $success-color;
    font-size: 0.9em;
  }

  p {
    // Specifically for service area paragraph if used
    line-height: 1.6;
    color: $text-color-secondary;
    margin: 0;
  }
}

/* Photo Gallery Section (Moved to Bottom) */
.photo-gallery-section {
  // Now uses .card style implicitly via HTML class="card"
  // Add specific overrides if needed

  h2 {
    // Style for h2 inside photo section if different
    // Inherits from .card h2 by default
  }

  .photo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 1rem;

    // Empty state for when there are no photos
    &:empty {
      display: none;

      & + .no-photos-message {
        display: block;
      }
    }

    // Single photo case - make it larger but not full width
    &.single-photo {
      grid-template-columns: minmax(250px, 400px);
      justify-content: center;
    }

    img {
      width: 100%;
      height: auto;
      max-height: 250px;
      object-fit: contain;
      border-radius: 4px;
      display: block;
      background-color: #f9f9f9;
      border: 1px solid $border-color-light;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .no-photos-message {
    display: none;
    color: $text-color-light;
    font-style: italic;
    margin-top: 1rem;
  }

  @media (min-width: $breakpoint-lg) {
    .photo-grid {
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));

      &.single-photo {
        grid-template-columns: minmax(300px, 500px);
      }
    }
  }
}