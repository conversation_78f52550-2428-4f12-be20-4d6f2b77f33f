import csv
import os
from collections import defaultdict
from string import Template
import math

# Function to convert rating to stars
def convert_rating_to_stars(rating):
    try:
        # Convert to float and round to nearest 0.5
        rating = round(float(rating) * 2) / 2
        
        # Calculate full stars
        full_stars = int(rating)
        
        # Check if there's a half star
        half_star = "½" if rating - full_stars == 0.5 else ""
        
        return "★" * full_stars + half_star
    except (ValueError, TypeError):
        return ""

# HTML template
html_template = Template('''---
title: "Best Accountants in $city, $state | Top Tax HQ"
description: "Find the best $type in $city, $state. Browse our comprehensive list of accounting professionals with ratings and reviews."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "$city_lower-$state_lower-accountants/"
eleventyNavigation:
    hideOnMobile: true
    hideOnDesktop: true
    key: $city
    order: 3001
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/city.css" />
    <style>
        .business-card img {
            max-width: 100%;
            height: auto;
            object-fit: cover;
        }
    </style>
{% endblock %}

{% block body %}

<section id="int-hero">
    <h1 id="home-h">Best Accountants in $city, $state</h1>
    <picture>
        <source media="(max-width: 600px)" srcset="/assets/images/banner-sm.webp" />
        <source media="(min-width: 601px)" srcset="/assets/images/banner.webp" />
        <img src="/assets/images/banner.webp" alt="accountant banner" loading="eager" width="2500" height="1667" />
    </picture>
</section>

<section class="business-listings">
    <div class="business-grid">
$business_cards
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const itemsPerPage = 12;
        const businessCards = document.querySelectorAll('.business-card');
        const totalItems = businessCards.length;
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        let currentPage = 1;

        function showPage(page) {
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;

            businessCards.forEach((card, index) => {
                card.style.display = index >= start && index < end ? 'block' : 'none';
            });

            document.querySelectorAll('.page-link').forEach(link => {
                link.classList.toggle('active', parseInt(link.textContent) === page);
            });
        }

        function createPagination() {
            const pagination = document.createElement('div');
            pagination.className = 'pagination';

            for (let i = 1; i <= totalPages; i++) {
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.textContent = i;
                pageLink.href = '#';
                pageLink.onclick = (e) => {
                    e.preventDefault();
                    currentPage = i;
                    showPage(currentPage);
                };
                pagination.appendChild(pageLink);
            }
            document.querySelector('.business-grid').after(pagination);
        }

        createPagination();
        showPage(1);
    });
</script>

{% endblock %}''')

def generate_html_from_csv(csv_file):
    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # First, count businesses per city
    business_counts = defaultdict(int)
    business_data = defaultdict(list)
    
    with open(csv_file, mode='r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            city = row.get('city', 'Unknown')
            state = row.get('state', 'Unknown')
            business_counts[(city, state)] += 1
            business_data[(city, state)].append(row)

    # Filter cities with more than 12 businesses
    for (city, state), businesses in list(business_data.items()):
        # Only process cities with more than 12 businesses
        if business_counts[(city, state)] <= 12:
            del business_data[(city, state)]
            continue

        city_lower = city.lower().replace(' ', '-')
        state_lower = state.lower()

        business_cards = ""
        for business in businesses:
            # Get photo URL
            photo_url = business.get('photo', '')
            
            # Convert stars with the updated method
            stars = convert_rating_to_stars(business.get('rating', '0'))
            
            business_cards += f'''<div class="business-card">
                <div class="business-image">
                    <img src="{photo_url}" alt="{business.get('name', 'N/A')}" loading="lazy" />
                </div>
                <h3>{business.get('name', 'N/A')}</h3>
                <div class="rating">
                    <span class="stars">{stars}</span>
                    <span class="review-count">({business.get('reviews', '0')} reviews)</span>
                </div>
                <p class="address">{business.get('type', 'N/A')}</p>
                <p class="address">{business.get('full_address', 'N/A')}</p>
                <p class="phone">{business.get('phone', 'N/A')}</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div>'''

        html_content = html_template.substitute(
            city=city,
            state=state,
            type="Accountant",
            business_cards=business_cards,
            city_lower=city_lower,
            state_lower=state_lower
        )

        filename = os.path.join(script_dir, f"{city_lower}-{state_lower}-accountants.html")
        with open(filename, mode='w', encoding='utf-8') as html_file:
            html_file.write(html_content)
        print(f"Generated: {filename}")

# Run script
generate_html_from_csv('data.csv')