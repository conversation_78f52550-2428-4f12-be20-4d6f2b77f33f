import csv
import os
from collections import defaultdict
from string import Template
import json

def generate_cities_page(csv_file):
    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Count businesses per city and state
    city_business_counts = defaultdict(int)
    state_cities = defaultdict(list)
    
    with open(csv_file, mode='r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            city = row.get('city', 'Unknown').strip()
            state = row.get('state', 'Unknown').strip()
            
            # Count businesses for each city
            city_business_counts[(city, state)] += 1
            
            # Add city to state list if not already present
            if city not in state_cities[state]:
                state_cities[state].append(city)
    
    # Filter cities with more than 12 businesses
    filtered_state_cities = {}
    for state, cities in state_cities.items():
        state_qualifying_cities = [
            city for city in cities 
            if city_business_counts[(city, state)] > 12
        ]
        
        if state_qualifying_cities:
            # Sort cities by number of businesses in descending order
            state_qualifying_cities.sort(
                key=lambda city: city_business_counts[(city, state)], 
                reverse=True
            )
            filtered_state_cities[state] = state_qualifying_cities
    
    # Sort states alphabetically
    sorted_states = sorted(filtered_state_cities.keys())
    
    # Prepare the HTML content
    cities_section_content = ""
    
    for state in sorted_states:
        cities = filtered_state_cities[state]
        
        # Split cities into initial 10 and additional cities
        initial_cities = cities[:10]
        additional_cities = cities[10:]
        
        # Create cities grid for this state
        cities_section_content += f'''
        <div class="state-section">
            <h2 class="state-name">Best Accounting & Tax Professionals in {state}</h2>
            <div class="cities-grid">
                {"".join([f'<a href="/{city.lower().replace(" ", "-")}-{state.lower()}-accountants/" class="city-link">'
                          f'<span class="city-name">{city}</span>'
                          f'<span class="business-count">Top {city_business_counts[(city, state)]} businesses ✔️</span>'
                          f'</a>' for city in initial_cities])}
            </div>
            {f'<div class="additional-cities">' + 
             ''.join([f'<a href="/{city.lower().replace(" ", "-")}-{state.lower()}-accountants/" class="city-link">'
                      f'<span class="city-name">{city}</span>'
                      f'<span class="business-count">Top {city_business_counts[(city, state)]} businesses ✔️</span>'
                      f'</a>' for city in additional_cities]) + 
             '</div>' if additional_cities else ''}
            {('<div class="city-buttons">'
              '<button class="show-more-cities">Show More Cities</button>'
              '<button class="hide-additional-cities" style="display:none;">Hide Additional Cities</button>'
              '</div>') if additional_cities else ''}
        </div>
        '''
    
    # HTML template (modified to remove inline script)
    html_template = Template('''---
title: "Best Accountants in the USA | Top Tax HQ"
description: "Find the best accountants in cities across the United States. Browse our comprehensive list of accounting professionals by city."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "cities/"
eleventyNavigation:
    key: Cities
    order: 300
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/cities.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    LANDING                   -->
    <!-- ============================================ -->
    <section id="int-hero">
        <h1 id="home-h">Best Accounting Services in the US</h1>
        <picture>
            <source media="(max-width: 600px)" srcset="/assets/images/banner-sm.webp" />
            <source media="(min-width: 601px)" srcset="/assets/images/banner.webp" />
            <img aria-hidden="true" decoding="async" src="/assets/images/banner.webp" alt="accounting banner"
                loading="eager" width="2500" height="1667" />
        </picture>
    </section>

    <section class="cities-sections">
        $cities_section_content
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const showMoreButtons = document.querySelectorAll('.show-more-cities');
            const hideButtons = document.querySelectorAll('.hide-additional-cities');
            
            showMoreButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const stateSection = button.closest('.state-section');
                    const additionalCitiesContainer = stateSection.querySelector('.additional-cities');
                    const additionalCities = additionalCitiesContainer.querySelectorAll('.city-link');
                    
                    // Show additional cities
                    additionalCities.forEach(city => {
                        city.style.display = 'flex';
                    });

                    // Display the additional cities container
                    additionalCitiesContainer.style.display = 'grid';
                    
                    // Hide show more button, show hide button
                    button.style.display = 'none';
                    stateSection.querySelector('.hide-additional-cities').style.display = 'block';
                });
            });

            hideButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const stateSection = button.closest('.state-section');
                    const additionalCitiesContainer = stateSection.querySelector('.additional-cities');
                    const additionalCities = additionalCitiesContainer.querySelectorAll('.city-link');
                    
                    // Hide additional cities
                    additionalCities.forEach(city => {
                        city.style.display = 'none';
                    });

                    // Hide the additional cities container
                    additionalCitiesContainer.style.display = 'none';
                    
                    // Hide hide button, show show more button
                    button.style.display = 'none';
                    stateSection.querySelector('.show-more-cities').style.display = 'block';
                });
            });
        });
    </script>

    {% include 'components/cta.html' %}
{% endblock %}''')

    # Generate the full HTML content
    html_content = html_template.substitute(
        cities_section_content=cities_section_content
    )

    # Write the HTML file
    filename = os.path.join(script_dir, "cities.html")
    with open(filename, mode='w', encoding='utf-8') as html_file:
        html_file.write(html_content)
    
    print(f"Generated: {filename}")
    print(f"Total states with qualifying cities: {len(sorted_states)}")
    print("Details of states and their qualifying cities:")
    for state in sorted_states:
        print(f"{state}: {len(filtered_state_cities[state])} cities")

# Run script
generate_cities_page('data.csv')