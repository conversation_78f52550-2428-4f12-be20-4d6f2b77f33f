---
title: "Blog | Top Tax HQ"
description: "Meta description for the page"
preloadImg: "/assets/images/banner-sm.webp"
permalink: "blog/"
eleventyNavigation:
    key: Blog
    order: 500
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/blog.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    LANDING                   -->
    <!-- ============================================ -->

    <section id="int-hero">
        <h1 id="home-h">Blog</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <!-- ============================================ -->
    <!--              Main Blog Content               -->
    <!-- ============================================ -->

    <div class="blog-container main-content-wrapper">
        <!--Main content -->
        <div class="main-content">
            <!-- ============================================ -->
            <!--                 Blog Articles                -->
            <!-- ============================================ -->

            {% if collections.post | length == 0 %}
                <h1>No Recent Posts</h1>
            {% else %}
                {%- for post in collections.post | reverse -%}
                    <article class="recent-articles">
                        <!--Main Article Image-->
                        <picture class="blog-mainImage">
                            <img
                                src="{{ post.data.image }}"
                                alt="{{ post.data.imageAlt }}"
                                width="795"
                                height="400"
                                decoding="async"
                            />
                        </picture>
                        <!--Article Info-->
                        <div class="article-group">
                            <div class="blog-authorGroup">
                                <!--Author Image-->
                                <picture class="blog-author-img">
                                    <img
                                        src="/assets/svgs/profile.svg"
                                        alt="house"
                                        width="32"
                                        height="32"
                                        decoding="async"
                                    />
                                </picture>
                                <span class="blog-author"
                                    >{{ post.data.author }}</span
                                >
                                <span
                                    aria-hidden="true"
                                    class="blog-dot"
                                ></span>
                                <!--Blog Date-->
                                <span class="blog-date"
                                    >{{ post.date | postDate }}</span
                                >
                            </div>
                            <h2 class="blog-h1">{{ post.data.title }}</h2>
                            <p class="blog-desc">{{ post.data.description }}</p>
                            <a href="{{ post.url }}" class="blog-link"
                                >Continue Reading</a
                            >
                        </div>
                    </article>
                {%- endfor -%}
            {% endif %}
        </div>

        {% include 'components/featured-post.html' %}
    </div>
{% endblock %}
