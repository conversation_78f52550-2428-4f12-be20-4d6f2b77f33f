---
title: "Top Tax HQ | Directory of the Best U.S. Accountants"
description: "Find trusted accountants and financial professionals across the United States. Your comprehensive resource for accounting services nationwide."
preloadImg: "/assets/images/hero-sm.webp"
permalink: "/"
tags: "sitemap"
eleventyNavigation:
    key: Home
    order: 100
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/local.css" />
    <link rel="stylesheet" href="/assets/css/critical.css" />

    <!-- Script for Netlify Identity -->
    <script
        defer
        src="https://identity.netlify.com/v1/netlify-identity-widget.js"
    ></script>
    <script defer>
        if (window.netlifyIdentity) {
            window.netlifyIdentity.on("init", (user) => {
                if (!user) {
                    window.netlifyIdentity.on("login", () => {
                        document.location.href = "/admin/";
                    });
                }
            });
        }
    </script>

    <!-- J<PERSON><PERSON> -->
    <!-- prettier-ignore-start -->
    <script type="application/ld+json">
        {
            "@context": "http://schema.org",
            "@type": "WebSite",
            "name": "Top Tax HQ",
            "url": "{{ client.domain }}",
            "description": "Premier directory of accountants and financial professionals across the United States",
            "potentialAction": {
                "@type": "SearchAction",
                "target": {
                    "@type": "EntryPoint",
                    "urlTemplate": "{{ client.domain }}/search?q={search_term_string}"
                },
                "query-input": "required name=search_term_string"
            },
            "publisher": {
                "@type": "Organization",
                "name": "Top Tax HQ",
                "description": "Connecting businesses and individuals with qualified accounting professionals nationwide",
                {% if client.email %}"email": "{{ client.email }}",{% endif %}
                {% if client.address %}
                "address": {
                    "@type": "PostalAddress",
                    "addressCountry": "US"
                },
                {% endif %}
                {% if client.socials %}
                "sameAs": [{% for platform, url in client.socials %}
                    {% if not loop.first %},{% endif %}
                    "{{ url }}"
                {% endfor %}]
                {% endif %}
            }
        }
    </script>
    <!-- prettier-ignore-end -->
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    Hero                      -->
    <!-- ============================================ -->

    <section id="hero-2041">
        <div class="cs-container">
            <div class="cs-content">
                <span class="cs-topper">US ACCOUNTANT DIRECTORY</span>
                <h1 class="cs-title">Top Tax HQ</h1>
                <p class="cs-text">
                    Find trusted accountants and financial professionals across
                    the United States. Your comprehensive resource for
                    accounting services nationwide.
                </p>
                <a href="/cities" class="cs-button-solid">Search Now</a>
            </div>
            <form class="cs-form" id="cs-form-2041" name="Landing Form">
                <label class="cs-label">
                    <div class="cs-flex">
                        City <span class="span cs-color">*</span>
                    </div>
                    <input
                        class="cs-input"
                        required
                        type="text"
                        id="city-2041"
                        name="city"
                        placeholder="e.g. San Francisco"
                    />
                </label>
                <label class="cs-label">
                    <div class="cs-flex">
                        State <span class="span cs-color">*</span>
                    </div>
                    <input
                        class="cs-input"
                        required
                        type="tel"
                        id="state-2041"
                        name="state"
                        placeholder="e.g. California"
                    />
                </label>
                <label class="cs-label">
                    <div class="cs-flex">
                        Service Needed <span class="span cs-color">*</span>
                    </div>
                    <input
                        class="cs-input"
                        required
                        type="text"
                        id="service-2041"
                        name="service"
                        placeholder="e.g. accountant"
                    />
                </label>
                <button class="cs-button-solid cs-submit" type="submit">
                    Search
                </button>
            </form>
        </div>

        <!-- Background Image -->
        <picture class="cs-background">
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/hero-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/hero.webp"
            />
            <img
                loading="lazy"
                decoding="async"
                src="/assets/images/hero.webp"
                alt="roof repair"
                width="1920"
                height="1200"
                aria-hidden="true"
            />
        </picture>
    </section>
    <script>
        document
            .getElementById("cs-form-2041")
            .addEventListener("submit", async function (e) {
                e.preventDefault();

                const city = document
                    .getElementById("city-2041")
                    .value.trim()
                    .toLowerCase()
                    .replace(/\s+/g, "-");
                const state = document
                    .getElementById("state-2041")
                    .value.trim()
                    .toLowerCase()
                    .replace(/\s+/g, "-");

                const slug = `/${city}-${state}-accountants`;

                try {
                    const res = await fetch(slug, { method: "HEAD" });

                    if (res.ok) {
                        window.location.href = slug;
                    } else {
                        window.location.href = "/no-website";
                    }
                } catch (error) {
                    window.location.href = "/no-website";
                }
            });
    </script>

    <!-- ============================================ -->
    <!--                 Side By Side                 -->
    <!-- ============================================ -->

    <section id="sbs">
        <div class="cs-container">
            <!-- Left Image Section -->
            <div class="cs-left">
                <picture class="cs-picture cs-picture1">
                    <img
                        aria-hidden="true"
                        decoding="async"
                        src="/assets/images/sbs1.webp"
                        alt="tax professional"
                        loading="lazy"
                        width="400"
                        height="563"
                    />
                </picture>
                <picture class="cs-picture cs-picture2">
                    <img
                        aria-hidden="true"
                        decoding="async"
                        src="/assets/images/sbs2.webp"
                        alt="accounting professional"
                        loading="lazy"
                        width="400"
                        height="563"
                    />
                </picture>
            </div>
            <!-- Right Content Section-->
            <div class="cs-right">
                <span class="cs-topper">About Us</span>
                <h2 class="cs-title">
                    Your Trusted Source for Professional Accountants
                </h2>
                <p class="cs-text">
                    Top Tax HQ is your go-to directory, connecting businesses
                    and individuals with a wide range of accounting
                    professionals across the United States. Our platform
                    provides easy access to accountants who offer a variety of
                    services, ensuring you can find the right fit for your
                    financial needs.
                </p>
                <p class="cs-text">
                    Whether you're a small business owner, entrepreneur, or
                    individual looking for financial expertise, we help you
                    discover the perfect accounting professional for your unique
                    requirements. Our directory spans all major cities across
                    the country, featuring specialists in various financial
                    fields.
                </p>
                <div class="cs-flex-group">
                    <p class="cs-flex-p">
                        "We're dedicated to helping Americans connect with
                        top-notch accounting professionals to support their
                        financial success."
                    </p>
                    <span class="cs-name">Top Tax HQ</span>
                    <span class="cs-job">National Accounting Directory</span>
                    <img
                        class="cs-quote-icon"
                        loading="lazy"
                        decoding="async"
                        src="https://csimg.nyc3.digitaloceanspaces.com/SideBySide/quote-white.svg"
                        alt="quote"
                        width="136"
                        height="77"
                    />
                </div>
                <a href="/about/" class="cs-button-solid">About Us</a>
            </div>
        </div>
    </section>

    <!-- ============================================ -->
    <!--                 Recommended                  -->
    <!-- ============================================ -->

    <section id="gallery-2297">
        <div class="cs-container">
            <div class="cs-content">
                <div class="cs-flex">
                    <span class="cs-topper">Recently Added</span>
                    <h2 class="cs-title">Recent Additions to our Directory</h2>
                    <p class="cs-text">
                        Explore the latest additions to our directory, where
                        you'll find a diverse range of accounting professionals
                        across the United States.
                    </p>
                </div>
            </div>
            <div class="cs-gallery-wrapper">
                <div class="cs-gallery">
                    <a class="cs-image" href="/all-in-accounting-solutions-llc/"
                        ><picture class="cs-picture"
                            >
                            <img
                                alt="gallery"
                                height="400"
                                src="https://lh3.googleusercontent.com/p/AF1QipOqBgm2XvQXAbTE1SuwAL92HymCtYRc3682dOqo=w408-h326-k-no"
                                width="305"
                                decoding="async"
                                loading="lazy"
                        /></picture>
                        <div class="cs-info">
                            <span class="cs-project">All In Accounting</span>
                            <span class="cs-tag">Lebanon, OR</span>
                        </div></a
                    ><a class="cs-image" href="/mile-high-cpas/"
                        ><picture class="cs-picture"
                            >
                            <img
                                alt="gallery"
                                height="400"
                                src="https://images.squarespace-cdn.com/content/v1/5e299f123bd0b35f812ee3fb/*************-P1S6HYBJML9H2LF66C8Q/MHCPA-Logo.png?format=750w"
                                width="305"
                                decoding="async"
                                loading="lazy"
                        /></picture>
                        <div class="cs-info">
                            <span class="cs-project">Mile High CPAs</span>
                            <span class="cs-tag">Denver, CO</span>
                        </div></a
                    ><a class="cs-image" href="/arthasmart-tax-solutions/"
                        ><picture class="cs-picture"
                            >
                            <img
                                alt="gallery"
                                height="400"
                                src="https://images.squarespace-cdn.com/content/v1/674f790113fb766439995e6b/eba72bec-a8ae-40d2-a69b-d1a9f3cf0d3e/AS-Logo-Horizontal.png?format=1500w"
                                width="305"
                                decoding="async"
                                loading="lazy"
                        /></picture>
                        <div class="cs-info">
                            <span class="cs-project"
                                >Arthasmart Tax Solutions</span
                            >
                            <span class="cs-tag">Issaquah, WA</span>
                        </div></a
                    >
                </div>
            </div>
        </div>
        <div class="cs-stats">
            <div class="cs-stat">
                <span class="cs-number">3</span>
                <span class="cs-desc">In Demand Accountants</span>
            </div>
            <div class="cs-stat">
                <span class="cs-number">20+</span>
                <span class="cs-desc">Services Offered</span>
            </div>
            <div class="cs-stat">
                <span class="cs-number">25+</span>
                <span class="cs-desc">Years Experience</span>
            </div>
            <div class="cs-stat">
                <span class="cs-number">100%</span>
                <span class="cs-desc">Locally Rooted</span>
            </div>
        </div>
    </section>

    {% include 'components/cta.html' %}
{% endblock %}
