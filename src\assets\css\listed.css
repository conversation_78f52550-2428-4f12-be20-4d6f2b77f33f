/* ============================================ */
/* Variable Definitions             */
/* ============================================ */
/* ============================================ */
/* Business Listing Styles           */
/* ============================================ */
.business-listing-container {
  max-width: 1100px;
  margin: 2rem auto;
  padding: 0 1rem;
}

/* Business Header */
.business-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}
.business-header .header-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
}
@media (min-width: 576px) {
  .business-header .header-content {
    flex-direction: row;
    align-items: center;
  }
}
.business-header .logo-container {
  flex-shrink: 0;
  margin-bottom: 0.5rem;
}
@media (min-width: 576px) {
  .business-header .logo-container {
    margin-bottom: 0;
  }
}
.business-header .business-logo {
  display: block;
  max-width: 100px;
  height: auto;
  border-radius: 4px;
  border: 1px solid #eee;
}
@media (min-width: 576px) {
  .business-header .business-logo {
    max-width: 120px;
  }
}
.business-header .info-container {
  flex-grow: 1;
}
.business-header .business-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.8rem;
  font-weight: bold;
  color: #333;
}
.business-header .rating-reviews {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}
.business-header .rating {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}
.business-header .rating .stars {
  font-size: 1.1rem;
}
.business-header .rating .rating-text {
  font-weight: bold;
  color: #555;
  font-size: 0.95rem;
}
.business-header .review-count {
  margin: 0;
  font-size: 0.95rem;
  color: #555;
}
.business-header .category {
  margin: 0.5rem 0 0 0;
  font-size: 0.95rem;
  color: #555;
}

/* Business Meta / Quick Contact */
.business-meta {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-top: 0;
}
.business-meta .contact-info-quick {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  margin-top: 0.5rem;
}
.business-meta .contact-info-quick a {
  text-decoration: none;
  color: var(--primary);
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
}
.business-meta .contact-info-quick a:hover {
  text-decoration: underline;
}
.business-meta .contact-info-quick a .fas {
  font-size: 1rem;
  color: #555;
}

/* Main Content Layout (2 columns on wider screens) */
.main-content-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}
@media (min-width: 768px) {
  .main-content-layout {
    grid-template-columns: 2fr 1fr;
  }
}
/* Card Style for Sections */
.card {
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
.card h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

/* Left Column Content */
/* Location & Hours Section */
.location-hours-section .location-hours-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
@media (min-width: 768px) {
  .location-hours-section .location-hours-content {
    flex-direction: row;
    align-items: flex-start;
  }
}
.location-hours-section .map-container {
  position: relative;
  height: 0;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #ddd;
  padding-bottom: 56.25%;
}
.location-hours-section .map-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
@media (min-width: 768px) {
  .location-hours-section .map-container {
    flex: 1 1 40%;
    height: 250px;
    padding-bottom: 0;
  }
}
.location-hours-section .address-hours-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
@media (min-width: 768px) {
  .location-hours-section .address-hours-info {
    flex: 1 1 55%;
  }
}
.location-hours-section .address p {
  margin: 0 0 0.5rem 0;
  line-height: 1.5;
}
.location-hours-section .address .directions-link {
  color: var(--primary);
  text-decoration: none;
  font-weight: bold;
}
.location-hours-section .address .directions-link:hover {
  text-decoration: underline;
}
.location-hours-section .hours ul {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 0.95rem;
  color: #333;
}
.location-hours-section .hours li {
  margin-bottom: 0.5rem;
  display: flex;
  justify-content: space-between;
  padding-bottom: 0.5rem;
  border-bottom: 1px dotted #eee;
}
.location-hours-section .hours li:last-child {
  border-bottom: none;
}
.location-hours-section .hours li .time {
  font-weight: 500;
  color: #555;
  text-align: right;
}
.location-hours-section .hours .hours-note {
  font-style: italic;
  color: #777;
  font-size: 0.9rem;
  display: block;
  margin-top: 0.5rem;
}

/* About Section */
.about-section p {
  line-height: 1.6;
  color: #555;
  margin-bottom: 1em;
}
.about-section p:last-child {
  margin-bottom: 0;
}

/* Right Column / Sidebar Content */
/* Highlights & Service Area Sections */
.highlights-section ul,
.service-area-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.highlights-section li,
.service-area-section li {
  margin-bottom: 0.75rem;
  color: #555;
  line-height: 1.5;
  display: flex;
  align-items: baseline;
  gap: 0.5rem;
}
.highlights-section li:last-child,
.service-area-section li:last-child {
  margin-bottom: 0;
}
.highlights-section .fas.fa-check,
.service-area-section .fas.fa-check {
  color: #28a745;
  font-size: 0.9em;
}
.highlights-section p,
.service-area-section p {
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Photo Gallery Section (Moved to Bottom) */
.photo-gallery-section .photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 1rem;
}
.photo-gallery-section .photo-grid:empty {
  display: none;
}
.photo-gallery-section .photo-grid:empty + .no-photos-message {
  display: block;
}
.photo-gallery-section .photo-grid.single-photo {
  grid-template-columns: minmax(250px, 400px);
  justify-content: center;
}
.photo-gallery-section .photo-grid img {
  width: 100%;
  height: auto;
  max-height: 250px;
  object-fit: contain;
  border-radius: 4px;
  display: block;
  background-color: #f9f9f9;
  border: 1px solid #eee;
  transition: transform 0.2s ease;
}
.photo-gallery-section .photo-grid img:hover {
  transform: scale(1.02);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}
.photo-gallery-section .no-photos-message {
  display: none;
  color: #777;
  font-style: italic;
  margin-top: 1rem;
}
@media (min-width: 992px) {
  .photo-gallery-section .photo-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
  .photo-gallery-section .photo-grid.single-photo {
    grid-template-columns: minmax(300px, 500px);
  }
}

/*# sourceMappingURL=listed.css.map */
