---
title: "Runyan CPA Firm | Top Tax HQ"
description: "Virtual CPA Firm focused on yourindividual and business needs"
preloadImg: "/assets/images/banner-sm.webp"
permalink: "runyan-cpa-firm/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/listed.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    LANDING                   -->
    <!-- ============================================ -->

    <section id="int-hero">
        <h1 id="home-h"><PERSON> Runyan CPA LLC</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <main class="business-listing-container">
        <section class="business-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="https://img1.wsimg.com/isteam/ip/922571bd-661a-4dee-8bff-b20841bad149/Logo%20No%20Background.png/:/rs=w:190,h:190,cg:true,m/cr=w:190,h:190/qt=q:95"
                        alt="Runyan CPA Firm Logo"
                        class="business-logo"
                        loading="lazy"
                        width="100"
                        height="100"
                    />
                </div>
                <div class="info-container">
                    <h2 class="business-title">
                        Zachary Runyan CPA LLC
                    </h2>
                    <p class="category">
                        Accounting, Tax Preparation, Bookkeeping
                    </p>
                </div>
            </div>
        </section>

        <section class="business-meta">
            <div class="contact-info-quick">
                <a href="tel:************" class="phone-link">(*************</a>
                <a
                    href="https://runyancpafirm.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="website-link"
                    >Business Website</a
                >
            </div>
        </section>

        <div class="main-content-layout">
            <div class="content-left">
                <section class="location-hours-section card">
                    <h2>Location & Hours</h2>
                    <div class="location-hours-content">
                        <div class="map-container" id="map">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3124.5123025228963!2d-105.2093926!3d38.***************!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6dc14f6dee6f0511%3A0xc5e6dfbbcaa673e6!2sZachary%20Runyan%20CPA%20LLC!5e0!3m2!1sen!2sus!4v1746460072168!5m2!1sen!2sus" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                        <div class="address-hours-info">
                            <div class="address">
                                <p>
                                    <strong>406 Barrett Ave</strong><br />
                                    Cañon City, CO 81212
                                </p>
                                <a
                                    href="https://maps.app.goo.gl/UYboJZcpxJsKWwVp6"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    class="directions-link"
                                    >Get Directions</a
                                >
                            </div>
                            <div class="hours">
                                <ul>
                                    <li>
                                        Mon:
                                        <span class="time"
                                            >8:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Tue:
                                        <span class="time"
                                            >8:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Wed:
                                        <span class="time"
                                            >8:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Thu:
                                        <span class="time"
                                            >8:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Fri:
                                        <span class="time"
                                            >8:00 AM - 5:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Sat: <span class="time">Closed</span>
                                    </li>
                                    <li>
                                        Sun: <span class="time">Closed</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="about-section card">
                    <h2>About the Business</h2>
                    <p>
                        Every person and business has unique accounting and tax needs. My firm's goal is to provide services that meet your specific goals. This means a solution that makes sense and drives value. Ultimately, I want to understand your pain points, find solutions, and be your strategic accounting/tax partner. My goal is to provide you the opportunity to be as hands on (or off) as you would like. Meaning that you will have more time to do what you want to do, instead of focusing on accounting and tax.
                    </p>
                    <p>
                        I also believe in transparency about what I offer, while trying to simplify the decision-making process. My general fee structure ranges from $75 per hour to $250 per hour depending on the service. I try to take the complexity of the work into consideration with my fees. I am also open to discussing other arrangements or pricing structure, as appropriate. 
                    </p>
                </section>
            </div>
            <aside class="content-right">
                <section class="highlights-section card">
                    <h2>Business Services</h2>
                    <ul>
                        <li>Tax Return Preparation and Planning</li>
                        <li>Entity Guidance/Business Formation</li>
                        <li>Business Accounting Services</li>
                        <li>Consulting/Advisory services</li>
                    </ul>
                </section>

                <section class="service-area-section card">
                    <h2>Service Area</h2>
                    <p>Cañon City, CO</p>
                </section>
            </aside>
        </div>
        <section class="photo-gallery-section card">
            <h2>Photos</h2>
            <!-- JavaScript will add 'single-photo' class if there's only one image -->
            <div class="photo-grid" id="photoGrid">
                <img
                    src="https://lh3.googleusercontent.com/p/AF1QipP-hEjNqnVyMBGoZASUBqodjOiHzpY2A7gIfiEm=s1360-w1360-h1020-rw"
                    alt="Business photo 1"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://img1.wsimg.com/isteam/ip/922571bd-661a-4dee-8bff-b20841bad149/blob-8112a60.png/:/cr=t:16.74%25,l:0%25,w:100%25,h:50%25/rs=w:1366,h:683,cg:true"
                    alt="Business photo 2"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://img1.wsimg.com/isteam/stock/4791/:/cr=t:0%25,l:16.64%25,w:66.73%25,h:100%25/rs=w:1200,h:1200,cg:true/fx-gs"
                    alt="Business photo 3"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
            </div>
            <p class="no-photos-message">
                No photos available for this business.
            </p>
            <script>
                // Add 'single-photo' class if there's only one image
                document.addEventListener("DOMContentLoaded", function () {
                    const photoGrid = document.getElementById("photoGrid");
                    const images = photoGrid.querySelectorAll("img");

                    if (images.length === 1) {
                        photoGrid.classList.add("single-photo");
                    } else if (images.length === 0) {
                        photoGrid.style.display = "none";
                        document.querySelector(
                            ".no-photos-message",
                        ).style.display = "block";
                    }
                });
            </script>
        </section>
    </main>

    {% include 'components/cta.html' %}
{% endblock %}
