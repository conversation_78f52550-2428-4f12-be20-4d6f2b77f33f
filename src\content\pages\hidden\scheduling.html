---
title: "Scheduling | Top Tax HQ"
description: "Schedule a demo with Top Tax HQ - get your business listed today!"
permalink: "scheduling/"
---

{% extends "layouts/base.html" %}

{% block head %}
  <script src="https://assets.calendly.com/assets/external/widget.js" type="text/javascript" async></script>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
    }

    .loader-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }

    .spinner {
      border: 6px solid #f3f3f3;
      border-top: 6px solid #003366;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .fade-in {
      animation: fadeIn 0.8s ease forwards;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .calendly-inline-widget {
      margin-top: 4rem;
      height: 100vh;
      opacity: 0;
      transition: opacity 0.5s ease;
    }
  </style>
{% endblock %}

{% block body %}

  <!-- Loader -->
  <div id="loader" class="loader-container">
    <div class="spinner"></div>
  </div>

  <!-- Calendly Embed -->
  <div id="calendly" class="calendly-inline-widget"
       data-url="https://calendly.com/toptaxhq/30min"
       style="min-width:320px; height:100vh;">
  </div>

  {% include 'components/cta.html' %}

  <script>
    window.addEventListener('load', function () {
      const calendly = document.getElementById('calendly');
      const loader = document.getElementById('loader');

      // Check repeatedly for the Calendly iframe
      const interval = setInterval(() => {
        const iframe = calendly.querySelector('iframe');
        if (iframe && iframe.complete) {
          loader.style.display = 'none';
          calendly.classList.add('fade-in');
          clearInterval(interval);
        }
      }, 300);

      // Optional: Stop checking after 10 seconds (fail-safe)
      setTimeout(() => {
        clearInterval(interval);
        loader.style.display = 'none';
        calendly.classList.add('fade-in');
      }, 3000);
    });
  </script>

{% endblock %}