/* PAGE-<PERSON><PERSON><PERSON><PERSON> STYLES FOR THE BLOG AND ARTICLE PAGES */

/*-- -------------------------- -->
<---        Core Styles         -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    .blog-container {
        margin: auto;
        max-width: calc(1120 / 16 * 1em);
        width: 94%;
    }

    .blog-link {
        z-index: 1;
        position: relative;
        display: inline-block;
        border-radius: calc(4 / 16 * 1rem);
        margin: auto;
        background-color: var(--headerColor);
        min-width: calc(150 / 16 * 1rem);
        /* prevents padding from adding to the width */
        box-sizing: border-box;
        padding: 0 calc(24 / 16 * 1rem);
        text-decoration: none;
        font-size: calc(16 / 16 * 1rem);
        /* 46px - 56px */
        line-height: clamp(2.875em, 5.5vw, 3.5em);
        font-weight: 700;
        color: #fff;
        transition: color 0.3s;
        text-align: center;

        &:before {
            z-index: -1;
            z-index: -1;
            position: absolute;
            top: 0;
            left: 0;
            content: "";
            opacity: 0;
            border: 1px solid var(--headerColor);
            border-radius: calc(4 / 16 * 1rem);
            background: #fff;
            height: 100%;
            width: 100%;
            box-sizing: border-box;
            transition: opacity 0.3s;
        }

        &:hover {
            color: var(--headerColor);
            &:before {
                opacity: 1;
            }
        }
    }
}

/*-- -------------------------- -->
<---           Header           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #header {
        z-index: 1;
        position: relative;

        .blog-container {
            padding: clamp(100px, 14vw, 200px) 0 clamp(50px, 11vw, 100px);
        }

        .blog-header {
            margin: 0;
            width: 100%;
            font-size: clamp(24px, 4vw, 48px);
            line-height: 1.2em;
            font-weight: 700;
            color: #fff;
            text-align: left;
        }

        .blog-bg {
            z-index: -1;
            position: absolute;
            top: 0;
            left: 0;
            display: block;
            height: 100%;
            width: 100%;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }

            &:before {
                z-index: 1;
                position: absolute;
                top: 0;
                left: 0;
                /* color overlay */
                content: "";
                opacity: 0.6;
                display: block;
                background: #000;
                height: 100%;
                width: 100%;
            }
        }
    }
}

/*-- -------------------------- -->
<---        Main Content        -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    .blog-container {
        padding: clamp(60px, 7vw, 100px) 0;
    }

    .main-content-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        row-gap: calc(50 / 16 * 1em);
    }

    .main-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        row-gap: calc(50 / 16 * 1em);
    }
}

/* Tablet - 1024px */
@media only screen and (min-width: 64rem) {
    .main-content-wrapper {
        flex-direction: row;
        align-items: flex-start;
        column-gap: calc(20 / 16 * 1em);
    }
}

/*-- -------------------------- -->
<---    Recent Blog Articles    -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    .recent-articles {
        max-width: calc(795 / 16 * 1em);
        width: 100%;
        overflow: hidden;
        flex: none;

        .blog-mainImage {
            position: relative;
            display: block;
            border-radius: calc(8 / 16 * 1em) calc(8 / 16 * 1em) 0 0;
            height: clamp(200px, 30vw, 400px);
            width: 100%;
            overflow: hidden;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        .article-group {
            border: 1px solid #ebebeb;
            border-radius: 0 0 calc(8 / 16 * 1em) calc(8 / 16 * 1em);
            padding: clamp(30px, 5vw, 50px);
        }

        .blog-author-img {
            position: relative;
            display: block;
            border-radius: 50%;
            height: calc(32 / 16 * 1em);
            width: calc(32 / 16 * 1em);
            overflow: hidden;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        .blog-authorGroup {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: calc(12 / 16 * 1rem);
        }

        .blog-author,
        .blog-date {
            font-size: calc(14 / 16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColor);
        }

        .blog-dot {
            display: block;
            border-radius: 50%;
            background-color: var(--primary);
            height: 3px;
            width: 3px;
        }

        .blog-h1 {
            margin: calc(16 / 16 * 1rem) 0;
            font-size: clamp(20px, 5vw, 32px);
            line-height: 1.4em;
            font-weight: 700;
            color: var(--headerColor);
        }

        .blog-desc {
            margin: calc(16 / 16 * 1rem) 0 calc(32 / 16 * 1rem);
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColor);
        }

        .blog-link {
            z-index: 1;
            position: relative;
            display: inline-block;
            border-radius: calc(4 / 16 * 1rem);
            margin: auto;
            background-color: var(--headerColor);
            min-width: calc(150 / 16 * 1rem);
            /* prevents padding from adding to the width */
            box-sizing: border-box;
            padding: 0 calc(24 / 16 * 1rem);
            text-decoration: none;
            font-size: calc(16 / 16 * 1rem);
            /* 46px - 56px */
            line-height: clamp(2.875em, 5.5vw, 3.5em);
            font-weight: 700;
            color: #fff;
            transition: color 0.3s;
            text-align: center;

            &:before {
                z-index: -1;
                z-index: -1;
                position: absolute;
                top: 0;
                left: 0;
                content: "";
                opacity: 0;
                border: 1px solid var(--headerColor);
                border-radius: calc(4 / 16 * 1rem);
                background: #fff;
                height: 100%;
                width: 100%;
                box-sizing: border-box;
                transition: opacity 0.3s;
            }

            &:hover {
                color: var(--headerColor);
                &:before {
                    opacity: 1;
                }
            }
        }
    }
}

/* Mobile - 1024px */
@media only screen and (min-width: 64rem) {
    .recent-articles {
        .blog-h1 {
            max-width: calc(500 / 16 * 1rem);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        .recent-articles {
            .blog-h1,
            .blog-desc,
            .blog-author,
            .blog-date {
                color: #f7f7f7;
            }
        }

        .blog-sidebar {
            .feature-h3,
            .feature-date,
            .blog-header {
                color: #f7f7f7;
            }
            .blog-header {
                &:after {
                    background: var(--primary);
                }
            }
        }

        .blog-article {
            .blog-h1,
            .blog-author,
            .blog-date {
                color: #f7f7f7;
            }

            #blog-content {
                h1,
                h2,
                h3,
                h4,
                h5,
                h6 {
                    color: #f7f7f7;
                }
                p,
                li {
                    opacity: 0.8;
                    color: #f7f7f7;
                }
                a {
                    color: #9dafe5;
                }
            }
        }
    }
}

/*-- -------------------------- -->
<---          Sidebar           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    .blog-sidebar {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        max-width: calc(795 / 16 * 1em);
        width: 100%;
        box-sizing: border-box;
        row-gap: calc(50 / 16 * 1em);

        .blog-featured-group {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            border: 1px solid #ebebeb;
            border-radius: calc(8 / 16 * 1em);
            width: 100%;
            padding: calc(35 / 16 * 1em) calc(30 / 16 * 1em);
        }

        .blog-header {
            position: relative;
            display: block;
            margin-bottom: calc(30 / 16 * 1rem);
            font-size: calc(20 / 16 * 1em);
            font-weight: bold;
            color: var(--headerColor);
            text-align: center;

            &:after {
                position: relative;
                content: "";
                display: block;
                border-radius: calc(4 / 16 * 1rem);
                margin: calc(16 / 16 * 1rem) auto;
                background: var(--headerColor);
                height: 3px;
                width: calc(50 / 16 * 1rem);
            }
        }

        .blog-feature {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            border-bottom: 1px solid #ebebeb;
            width: 100%;
            padding: calc(20 / 16 * 1rem) 0;
            text-decoration: none;
            column-gap: calc(24 / 16 * 1em);

            &:nth-of-type(1) {
                padding-top: 0;
            }

            &:last-of-type {
                border: none;
                padding-bottom: 0;
            }
        }

        .blog-featureImage {
            position: relative;
            display: block;
            border-radius: 50%;
            height: calc(60 / 16 * 1em);
            width: calc(60 / 16 * 1em);
            overflow: hidden;
            flex: none;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        .feature-h3 {
            display: block;
            margin: 0;
            margin-bottom: calc(4 / 16 * 1rem);
            text-decoration: none;
            font-size: calc(15 / 16 * 1rem);
            line-height: 1.5em;
            font-weight: 700;
            color: var(--headerColor);
        }

        .feature-date {
            display: block;
            font-size: calc(14 / 16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColor);
        }
    }
}

/* Tablet - 1024px */
@media only screen and (min-width: 64rem) {
    .blog-sidebar {
        max-width: calc(360 / 16 * 1em);
    }
}

/*-- -------------------------- -->
<---        Blog Articles       -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    .blog-article {
        display: block;
        max-width: calc(795 / 16 * 1em);
        width: 100%;
        .blog-mainImage {
            position: relative;
            display: block;
            border-radius: calc(8 / 16 * 1em);
            margin-bottom: calc(50 / 16 * 1em);
            height: clamp(200px, 30vw, 400px);
            width: 100%;
            overflow: hidden;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        .blog-author-img {
            position: relative;
            display: block;
            border-radius: 50%;
            height: calc(32 / 16 * 1em);
            width: calc(32 / 16 * 1em);
            overflow: hidden;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        .blog-authorGroup {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: calc(12 / 16 * 1rem);
        }

        .blog-author,
        .blog-date {
            font-size: calc(14 / 16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColor);
        }

        .blog-dot {
            display: block;
            border-radius: 50%;
            background-color: #fe4f70;
            height: 3px;
            width: 3px;
        }

        .blog-h1 {
            margin: calc(16 / 16 * 1rem) 0;
            max-width: calc(550 / 16 * 1rem);
            font-size: clamp(20px, 5vw, 39px);
            line-height: 1.4em;
            font-weight: 700;
            color: var(--headerColor);
        }

        #blog-content {
            border-top: 1px solid #ebebeb;
            margin-top: calc(16 / 16 * 1em);
            padding-top: calc(25 / 16 * 1em);

            h1 {
                margin-bottom: 1rem;
                font-size: 2.25rem;
                line-height: 1.75em;
                font-weight: 700;
                color: var(--headerColor);
            }

            h2 {
                margin-bottom: calc(16 / 16 * 1rem);
                font-size: calc(28 / 16 * 1rem);
                line-height: 1.5em;
                font-weight: 700;
                color: var(--headerColor);
            }

            h3 {
                margin-bottom: calc(16 / 16 * 1rem);
                font-size: calc(24 / 16 * 1rem);
                line-height: 1.5em;
                font-weight: 700;
                color: var(--headerColor);
            }

            h4,
            h5,
            h6 {
                margin-bottom: calc(16 / 16 * 1rem);
                font-size: calc(20 / 16 * 1rem);
                line-height: 1.5em;
                font-weight: 700;
                color: var(--headerColor);
            }

            p {
                margin: 0;
                margin-bottom: calc(16 / 16 * 1rem);
                font-size: calc(16 / 16 * 1rem);
                line-height: 1.6em;
                color: var(--bodyTextColor);
            }

            a {
                text-decoration: underline;
                font-size: inherit;
                color: var(--secondary);
            }

            ul,
            ol {
                margin: calc(16 / 16 * 1rem) 0;
                padding-left: calc(40 / 16 * 1rem);

                li {
                    margin-bottom: calc(16 / 16 * 1rem);
                    list-style: circle;
                    font-size: calc(16 / 16 * 1rem);
                    line-height: 1.6em;
                    color: var(--bodyTextColor);
                }
            }

            img {
                display: block;
                border-radius: calc(8 / 16 * 1rem);
                margin: calc(16 / 16 * 1rem) 0;
                height: auto;
                width: 100%;
            }
        }
    }
}

/*-- -------------------------- -->
<---           Footer           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #cs-footer {
        background-color: #1a1a1a;
        /* 60px - 100px top and bottom */
        padding: clamp(3.75em, 7.82vw, 6.25em) calc(16 / 16 * 1em);
        font-family: "Maven Pro", "Arial", sans-serif;

        .cs-container {
            margin: auto;
            max-width: calc(1280 / 16 * 1em);
            width: 100%;
        }

        .cs-logo-group {
            position: relative;
            /* takes up all the space, lets the other ul's wrap below it */
            width: 100%;
        }

        .cs-logo {
            display: block;
            /* 28px - 44px */
            margin-bottom: clamp(1.75em, 4.17vw, 2.75em);
            height: auto;
            /* 210px - 240px */
            width: clamp(13.125em, 8vw, 15em);
        }

        .cs-logo-img {
            height: auto;
            width: 100%;
        }

        .cs-text {
            margin: 0;
            margin-bottom: calc(40 / 16 * 1rem);
            /* changes to 521px at desktop */
            max-width: calc(344 / 16 * 1rem);
            color: var(--bodyTextColorWhite);
        }

        .cs-nav {
            margin: 0;
            /* 32px - 40px */
            margin-bottom: clamp(2em, 5.3vw, 2.5em);
            padding: 0;
            padding-left: calc(16 / 16 * 1em);
        }

        .cs-nav-li {
            list-style: none;
            line-height: 1.5em;
            &:last-of-type {
                margin-bottom: 0;
            }
        }

        .cs-header {
            position: relative;
            display: block;
            margin-bottom: calc(20 / 16 * 1rem);
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            font-weight: 700;
            color: var(--bodyTextColorWhite);
        }

        .cs-nav-link {
            position: relative;
            text-decoration: none;
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColorWhite);

            &:before {
                position: absolute;
                bottom: calc(-2 / 16 * 1rem);
                left: 0;
                /* underline */
                content: "";
                opacity: 1;
                display: block;
                /* current color of the parent */
                background: currentColor;
                height: calc(2 / 16 * 1rem);
                width: 0%;
                transition: width 0.3s;
            }

            &:hover {
                &:before {
                    width: 100%;
                }
            }
        }

        .cs-bottom {
            position: relative;
            width: 100%;
            padding-left: calc(16 / 16 * 1em);
        }

        .cs-copyright {
            display: block;
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            color: #b4b2c7;
        }

        .cs-social {
            position: absolute;
            bottom: 0;
            right: calc(16 / 16 * 1em);
            display: inline-flex;
            flex-direction: column;
            justify-content: flex-start;
            gap: calc(12 / 16 * 1em);
        }

        .cs-social-link {
            z-index: 1;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background-color: #4e4b66;
            height: calc(24 / 16 * 1rem);
            width: calc(24 / 16 * 1rem);
            transition:
                transform 0.3s,
                background-color 0.3s;

            &:hover {
                transform: translateY(calc(-3 / 16 * 1rem));
                background-color: var(--primary);
            }
        }

        .cs-social-img {
            display: block;
            height: calc(13 / 16 * 1rem);
            width: auto;
        }
    }
}

/* Inbetween - 600px */
@media only screen and (min-width: 37.5em) {
    #cs-footer {
        .cs-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            align-items: flex-start;
            /* 48px - 60px */
            column-gap: clamp(3em, 5.9vw, 3.75em);
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #cs-footer {
        .cs-logo-group {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            /* 20px - 60px */
            margin-bottom: clamp(1.25em, 4vw, 3.75em);
        }

        .cs-logo {
            margin: 0;
        }

        .cs-text {
            margin: 0;
            max-width: calc(521 / 16 * 1rem);
            width: 50%;
            text-align: right;
        }

        .cs-nav {
            padding: 0;
        }

        .cs-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
        }

        .cs-social {
            position: relative;
            /* reset these positions */
            bottom: auto;
            right: auto;
            flex-direction: row;
        }
    }
}
