/* PAGE-SPEC<PERSON>IC STYLES FOR THE ABOUT PAGE */

/*-- -------------------------- -->
<---       Side By Side         -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #sbs {
        padding: var(--sectionPadding);

        .cs-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: auto;
            max-width: calc(1280 / 16 * 1rem);
            width: 100%;
            row-gap: calc(40 / 16 * 1rem);
        }

        .cs-left {
            position: relative;
            height: calc(636 / 16 * 1em);
            /* using ems so we can use font size to scale the whole section */
            width: calc(631 / 16 * 1em);
            /* scaling the font size with the view width */
            font-size: min(2.31vw, 0.7em);
        }

        .cs-picture {
            position: absolute;
            display: block;
            border-radius: calc(24 / 16 * 1em);
            /* clips img tag corners */
            overflow: hidden;
            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                /* makes image act like a background image */
                object-fit: cover;
            }
        }

        .cs-picture1 {
            top: 0;
            left: 0;
            height: calc(581 / 16 * 1em);
            width: calc(522 / 16 * 1em);
        }

        .cs-picture2 {
            bottom: 0;
            right: 0;
            /* 6px - 12px */
            border: clamp(0.375em, 1.5vw, 0.75em) solid #fff;
            background-color: #fff;
            height: calc(400 / 16 * 1em);
            width: calc(414 / 16 * 1em);
            box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 40px;
        }

        .cs-right {
            margin: auto;
            max-width: calc(542 / 16 * 1rem);
        }

        .cs-topper {
            margin-bottom: calc(4 / 16 * 1rem);
            text-align: left;
        }

        .cs-title {
            max-width: calc(800 / 16 * 1rem);
            text-align: left;
        }

        .cs-text {
            margin-bottom: calc(16 / 16 * 1rem);
            max-width: calc(750 / 16 * 1rem);
            color: var(--bodyTextColor);
            text-align: left;

            &:last-of-type {
                margin-bottom: calc(32 / 16 * 1rem);
            }
        }

        .cs-flex-group {
            position: relative;
            border-radius: calc(16 / 16 * 1rem);
            background-color: #f7f7f7;
            /* 16px - 32px */
            padding: clamp(1rem, 3vw, 2rem);
        }

        .cs-flex-p {
            margin: 0 0 calc(16 / 16 * 1rem);
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            line-height: 1.5em;
            color: #353535;
        }

        .cs-name {
            display: block;
            margin: 0 0 calc(4 / 16 * 1rem);
            text-transform: uppercase;
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.2em;
            font-weight: bold;
            color: var(--headerColor);
        }

        .cs-job {
            display: block;
            font-size: calc(14 / 16 * 1rem);
            line-height: 1.5em;
            color: #353535;
        }

        .cs-quote-icon {
            position: absolute;
            bottom: calc(0 / 16 * 1rem);
            /* 16px - 32px */
            right: clamp(1rem, 4vw, 2rem);
            height: auto;
            /* 60px - 136px */
            width: clamp(3.75rem, 10vw, 8.5rem);
        }

        .cs-button-solid {
            margin-top: calc(32 / 16 * 1rem);
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #sbs {
        .cs-container {
            flex-flow: row;
            justify-content: space-between;
            gap: calc(52 / 16 * 1rem);
        }

        .cs-left {
            font-size: min(1.2vw, 1em);
            flex: none;
        }

        .cs-right {
            margin: 0;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #sbs {
            .cs-left {
                &:before,
                &:after {
                    background: var(--accent);
                }
            }

            .cs-picture2 {
                /* 6px - 12px */
                border: clamp(0.375em, 1.5vw, 0.75em) solid var(--dark);
                background-color: var(--dark);
            }

            .cs-topper {
                color: var(--primaryLight);
            }

            .cs-title,
            .cs-text,
            .cs-h3,
            .cs-flex-p,
            .cs-name {
                color: var(--bodyTextColorWhite);
            }

            .cs-flex-group {
                background-color: var(--accent);
            }

            .cs-job {
                opacity: 0.8;
                color: var(--bodyTextColorWhite);
            }

            .cs-quote-icon {
                opacity: 0.2;
            }
        }
    }
}
