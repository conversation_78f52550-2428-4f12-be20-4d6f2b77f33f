---
title: "AOA Bookkeeping, LLC | Top Tax HQ"
description: "Being from Seattle, I have a place in my heart for the small business owner and if I can do my part to help them thrive then it makes everything I've learned that much more satisfying.     "
preloadImg: "/assets/images/banner-sm.webp"
permalink: "aoa-bookkeeping-llc/"
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/listed.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    LANDING                   -->
    <!-- ============================================ -->

    <section id="int-hero">
        <h1 id="home-h">AOA Bookkeeping, LLC</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="accounting banner"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

    <main class="business-listing-container">
        <section class="business-header">
            <div class="header-content">
                <div class="logo-container">
                    <img
                        src="https://images.squarespace-cdn.com/content/v1/57f4813aebbd1a79fbf32d2b/*************-GJUQLW8IAJRJ0U3JQJ3W/Elite+digital+badge+image.png?format=2500w"
                        alt="AOA Bookkeeping, LLC Logo"
                        class="business-logo"
                        loading="lazy"
                        width="100"
                        height="100"
                    />
                </div>
                <div class="info-container">
                    <h2 class="business-title">
                        AOA Bookkeeping, LLC
                    </h2>
                    <div class="rating-reviews">
                        <div class="rating">
                            <span class="stars" aria-label="5 out of 5 stars">
                                <span style="color: #f8ce0b;">★★★★★</span
                                ><span style="color: #e0e0e0;"></span>
                            </span>
                            <span class="rating-text">5.0</span>
                        </div>
                        <p class="review-count">(1 Reviews)</p>
                    </div>
                    <p class="category">
                        Account Cleanup, Bookkeeping, Payroll
                    </p>
                </div>
            </div>
        </section>

        <section class="business-meta">
            <div class="contact-info-quick">
                <a href="tel:************" class="phone-link">(*************</a>
                <a
                    href="https://www.oghalea.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="website-link"
                    >Business Website</a
                >
            </div>
        </section>

        <div class="main-content-layout">
            <div class="content-left">
                <section class="location-hours-section card">
                    <h2>Location & Hours</h2>
                    <div class="location-hours-content">
                        <div class="map-container" id="map">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2689.**********43!2d-122.33717449999999!3d47.6140438!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x549015be3b2f8c25%3A0x98fbcb22bcb9ebf7!2sAOA%20Bookkeeping%2C%20LLC!5e0!3m2!1sen!2sus!4v1745655476547!5m2!1sen!2sus" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                        </div>
                        <div class="address-hours-info">
                            <div class="address">
                                <p>
                                    <strong>600 Stewart St Ste 400</strong><br />
                                    Seattle, WA 98101
                                </p>
                                <a
                                    href="https://maps.app.goo.gl/GANpQMzsutT3SvME8"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    class="directions-link"
                                    >Get Directions</a
                                >
                            </div>
                            <div class="hours">
                                <ul>
                                    <li>
                                        Mon:
                                        <span class="time"
                                            >10:30 AM - 6:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Tue:
                                        <span class="time"
                                            >10:30 AM - 6:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Wed:
                                        <span class="time"
                                            >10:30 AM - 6:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Thu:
                                        <span class="time"
                                            >10:30 AM - 6:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Fri:
                                        <span class="time"
                                            >10:30 AM - 6:00 PM</span
                                        >
                                    </li>
                                    <li>
                                        Sat: <span class="time">Closed</span>
                                    </li>
                                    <li>
                                        Sun: <span class="time">Closed</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="about-section card">
                    <h2>About the Business</h2>
                    <p>
                        Lifting the burden, so you can focus on what's important.
Spend your time where it's most needed, on your business.  Let AOA Bookkeeping, LLC take care of your bookkeeping needs.  I offer affordable options to choose from and I will also work with your Accountant to save you time and preparation fees. 
                    </p>
                    <p>
                        I have been in the field of Finance for over 21 years now.  I started out as an Accounts Payable Clerk, which is where I started to hone my skills.  I was lucky to have worked for a business that thrived on cross training its employees.
                    </p>
                </section>
            </div>
            <aside class="content-right">
                <section class="highlights-section card">
                    <h2>Business Services</h2>
                    <ul>
                        <li>Cash Based & Accrual Based Record Keeping</li>
                        <li>Monthly Bank Reconciliation</li>
                        <li>Balance Sheet & Profit & Loss Statements</li>
                        <li>Payroll Services</li>
                        <li>Asset/Depreciation Management</li>
                    </ul>
                </section>

                <section class="service-area-section card">
                    <h2>Service Area</h2>
                    <p>Seattle, WA</p>
                </section>
            </aside>
        </div>
        <section class="photo-gallery-section card">
            <h2>Photos</h2>
            <!-- JavaScript will add 'single-photo' class if there's only one image -->
            <div class="photo-grid" id="photoGrid">
                <img
                    src="https://images.squarespace-cdn.com/content/v1/57f4813aebbd1a79fbf32d2b/*************-GJUQLW8IAJRJ0U3JQJ3W/Elite+digital+badge+image.png?format=2500w"
                    alt="Business photo 1"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
                <img
                    src="https://www.google.com/maps/vt/data=cid9URFunIOUs5nkzAIxMyHkwifISHq0kPDZahyeRgL0td0H0mjA2K5JgPe6D82IyyT7PJjJGP8ykvYbqttzbvcpHWeSuX6xYSctW_zJbk1_VG7RZwipN0mnfQmzf-cx_I8OulFwEjli434h6rjWAbbNGiW6jvvvTNh3PZhAX68etSAhGQ"
                    alt="Business photo 2"
                    loading="lazy"
                    decoding="async"
                    width="300"
                    height="200"
                />
            </div>
            <p class="no-photos-message">
                No photos available for this business.
            </p>
            <script>
                // Add 'single-photo' class if there's only one image
                document.addEventListener("DOMContentLoaded", function () {
                    const photoGrid = document.getElementById("photoGrid");
                    const images = photoGrid.querySelectorAll("img");

                    if (images.length === 1) {
                        photoGrid.classList.add("single-photo");
                    } else if (images.length === 0) {
                        photoGrid.style.display = "none";
                        document.querySelector(
                            ".no-photos-message",
                        ).style.display = "block";
                    }
                });
            </script>
        </section>
    </main>

    {% include 'components/cta.html' %}
{% endblock %}
