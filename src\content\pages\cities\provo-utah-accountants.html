---
title: "Best Accountants in Provo, Utah | Top Tax HQ"
description: "Find the best Accountant in Provo, Utah. Browse our comprehensive list of accounting professionals with ratings and reviews."
preloadImg: "/assets/images/banner-sm.webp"
permalink: "provo-utah-accountants/"
eleventyNavigation:
    hideOnMobile: true
    hideOnDesktop: true
    key: Provo
    order: 3001
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/city.css" />
    <style>
        .business-card img {
            max-width: 100%;
            height: auto;
            object-fit: cover;
        }
    </style>
{% endblock %}

{% block body %}

<section id="int-hero">
    <h1 id="home-h">Best Accountants in Provo, Utah</h1>
    <picture>
        <source media="(max-width: 600px)" srcset="/assets/images/banner-sm.webp" />
        <source media="(min-width: 601px)" srcset="/assets/images/banner.webp" />
        <img src="/assets/images/banner.webp" alt="accountant banner" loading="eager" width="2500" height="1667" />
    </picture>
</section>

<section class="business-listings">
    <div class="business-grid">
<div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/p/AF1QipOOlHiNT01qoyKUo2XcXdbosMmsinn_mTeFpTHy=w426-h240-k-no" alt="Paramount Tax & Accounting CPAs Provo" loading="lazy" />
                </div>
                <h3>Paramount Tax & Accounting CPAs Provo</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(111 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">288 W Center St Ste 201, Provo, UT 84601</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=Y3GJloYKPu-N0ti7tJ-YDA&cb_client=search.gws-prod.gps&w=408&h=240&yaw=99.88819&pitch=0&thumbfov=100" alt="Tax Pro of Provo" loading="lazy" />
                </div>
                <h3>Tax Pro of Provo</h3>
                <div class="rating">
                    <span class="stars">★★★★½</span>
                    <span class="review-count">(54 reviews)</span>
                </div>
                <p class="address">Tax preparation</p>
                <p class="address">398 N 500 W, Provo, UT 84601</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/gps-cs-s/AB5caB-OfTL-VrWwosNsrCVQPhxrDgHL57p_s6eWBMnNRCDU0CG7LPG6IQ036WJvLRI3cpNSaLWJrdPLGMb-PF99-p1CYdi2feocdhGoQ9WgdFeJZHvj_YrARvEna1LvKpVO9nNe0yd6MQ=w408-h272-k-no" alt="Joseph M Larsen CPA PC" loading="lazy" />
                </div>
                <h3>Joseph M Larsen CPA PC</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(50 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">3549 N University Ave #225, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/p/AF1QipNBgG1NsOKdXFlo6NZ1ZgpdOs1IZ_sVkv5olFBw=w408-h306-k-no" alt="The Accounting Guys" loading="lazy" />
                </div>
                <h3>The Accounting Guys</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(46 reviews)</span>
                </div>
                <p class="address">Accounting firm</p>
                <p class="address">4778 N 300 W #200, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/gps-cs-s/AB5caB-kkSUDxOfqQ81q8BGMuQ_AOPE7gPuhqCtbr1bp7iFI-nM2bKMTjH6CYl6Cmcee91rCl56uwZQ2QNhEMl5giWAbBx5DkTTDsB7CggK0KTbZQsU_s9CGi75X1d14aTqtftRmGnVp=w408-h321-k-no" alt="Gilbert & Stewart" loading="lazy" />
                </div>
                <h3>Gilbert & Stewart</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(41 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">190 W 800 N St #100, Provo, UT 84601</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/gps-cs-s/AB5caB_QqCX2O_pj-3jH7wbQZZ9M8kJUAGMAyGz0F1KD5c-dyOCpJglDgLp4TGqXGu6-PAS1gGxd4S9wEnvAezhBO8pmxZHkPVzyFELm0vJ78sHkKL5MaeBiO8TJKA6cwkueMQfGN-Cq=w408-h240-k-no-pi-0-ya350.722-ro-0-fo100" alt="DaleTax" loading="lazy" />
                </div>
                <h3>DaleTax</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(37 reviews)</span>
                </div>
                <p class="address">Tax preparation service</p>
                <p class="address">86 N University Ave Suite 360, Provo, UT 84601</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=iAt3ieY1NiXeGmNsdJ0AGA&cb_client=search.gws-prod.gps&w=408&h=240&yaw=189.58173&pitch=0&thumbfov=100" alt="Growify CPA Tax & Bookkeeping Services" loading="lazy" />
                </div>
                <h3>Growify CPA Tax & Bookkeeping Services</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(30 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">180 N University Ave #270, Provo, UT 84601</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/p/AF1QipPQE1M59JrhgcY-OO9_1qUGQxiT0_1YHDO1YObF=w408-h306-k-no" alt="Biesinger & Kofford CPA's" loading="lazy" />
                </div>
                <h3>Biesinger & Kofford CPA's</h3>
                <div class="rating">
                    <span class="stars">★★★★½</span>
                    <span class="review-count">(13 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">4778 N 300 W #200, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=26AN8iLnsCtylR0YW6gnsA&cb_client=search.gws-prod.gps&w=408&h=240&yaw=80.189835&pitch=0&thumbfov=100" alt="Duckworth & Gordon PLLC" loading="lazy" />
                </div>
                <h3>Duckworth & Gordon PLLC</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(10 reviews)</span>
                </div>
                <p class="address">Tax preparation service</p>
                <p class="address">3550 N University Ave # 300, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/p/AF1QipPPfR3sJrcUYTMhbY_n-ZFVKvz5jjOhBgSgy_am=w408-h306-k-no" alt="Harker & Associates, CPA" loading="lazy" />
                </div>
                <h3>Harker & Associates, CPA</h3>
                <div class="rating">
                    <span class="stars">★★★★½</span>
                    <span class="review-count">(8 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">1838 N 1120 W, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=J4v4uD-5WvrtsrznWU8PbA&cb_client=search.gws-prod.gps&w=408&h=240&yaw=67.4679&pitch=0&thumbfov=100" alt="Lunsford & Peck LLC" loading="lazy" />
                </div>
                <h3>Lunsford & Peck LLC</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(5 reviews)</span>
                </div>
                <p class="address">Certified public accountant</p>
                <p class="address">5132 N 300 W #200, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/p/AF1QipMkGAFIcr9FQIBl3CpkLIqpi20m4qwO-_w5aWnV=w408-h408-k-no" alt="Stevenson Smith CPAs" loading="lazy" />
                </div>
                <h3>Stevenson Smith CPAs</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(4 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">5252 N Edgewood Dr #350, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=0IyC8KQYlsGfq0eBIpxRNA&cb_client=search.gws-prod.gps&w=408&h=240&yaw=278.2663&pitch=0&thumbfov=100" alt="RIDGELINE ACCOUNTING" loading="lazy" />
                </div>
                <h3>RIDGELINE ACCOUNTING</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(3 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">2230 N University Pkwy #5b, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://lh3.googleusercontent.com/p/AF1QipNbQ6m8RpXqfd0WEvrZah7qRptoSGxoK0Q7ocmC=w408-h272-k-no" alt="Hortin Steven K CPA" loading="lazy" />
                </div>
                <h3>Hortin Steven K CPA</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(1 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">5255 N Edgewood Dr # 225, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=hSX7mmcr-EBI-Ex5_o9AOg&cb_client=search.gws-prod.gps&w=408&h=240&yaw=265.50027&pitch=0&thumbfov=100" alt="Sadler Gibb and Associates" loading="lazy" />
                </div>
                <h3>Sadler Gibb and Associates</h3>
                <div class="rating">
                    <span class="stars">★★★★★</span>
                    <span class="review-count">(1 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">3507 N University Ave #100, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=5x25iT4PqlC6efmo6NhbFg&cb_client=search.gws-prod.gps&w=408&h=240&yaw=182.53035&pitch=0&thumbfov=100" alt="Jon B White" loading="lazy" />
                </div>
                <h3>Jon B White</h3>
                <div class="rating">
                    <span class="stars"></span>
                    <span class="review-count">(0 reviews)</span>
                </div>
                <p class="address">Accounting firm</p>
                <p class="address">1838 N 1120 W, Provo, UT 84604</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div><div class="business-card">
                <div class="business-image">
                    <img src="https://streetviewpixels-pa.googleapis.com/v1/thumbnail?panoid=tPm6ZGtNnKgFZRb5a8ABSQ&cb_client=search.gws-prod.gps&w=408&h=240&yaw=72.46301&pitch=0&thumbfov=100" alt="Andon Financial" loading="lazy" />
                </div>
                <h3>Andon Financial</h3>
                <div class="rating">
                    <span class="stars"></span>
                    <span class="review-count">(0 reviews)</span>
                </div>
                <p class="address">Accountant</p>
                <p class="address">180 N University Ave, Provo, UT 84601</p>
                <p class="phone">(*************</p>
                <a href="/no-website" class="view-website-btn">View Website</a>
            </div>
    </div>
</section>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const itemsPerPage = 12;
        const businessCards = document.querySelectorAll('.business-card');
        const totalItems = businessCards.length;
        const totalPages = Math.ceil(totalItems / itemsPerPage);
        let currentPage = 1;

        function showPage(page) {
            const start = (page - 1) * itemsPerPage;
            const end = start + itemsPerPage;

            businessCards.forEach((card, index) => {
                card.style.display = index >= start && index < end ? 'block' : 'none';
            });

            document.querySelectorAll('.page-link').forEach(link => {
                link.classList.toggle('active', parseInt(link.textContent) === page);
            });
        }

        function createPagination() {
            const pagination = document.createElement('div');
            pagination.className = 'pagination';

            for (let i = 1; i <= totalPages; i++) {
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.textContent = i;
                pageLink.href = '#';
                pageLink.onclick = (e) => {
                    e.preventDefault();
                    currentPage = i;
                    showPage(currentPage);
                };
                pagination.appendChild(pageLink);
            }
            document.querySelector('.business-grid').after(pagination);
        }

        createPagination();
        showPage(1);
    });
</script>

{% endblock %}