/*-- -------------------------- -->
<---           Hero             -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #hero-2041 {
    /* 200px - 300px - top leaving extra space for the navigation */
    /* 60px - 100px bottom */
    padding: clamp(12.5rem, 25.95vw, 18.75em) 1rem clamp(1.75rem, 7.82vw, 3.25rem);
    /* prevents the topper line from causing an overflow */
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
  #hero-2041 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    /* 60px - 180px */
    gap: clamp(3.75rem, 13vw, 11.25rem);
  }
  #hero-2041 .cs-content {
    text-align: center;
    width: 100%;
    max-width: 53.125rem;
    /* prevents padding from affecting height and width */
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
  }
  #hero-2041 .cs-topper {
    line-height: 1em;
    padding: 0.25rem 1rem;
    background-color: #fff;
    color: var(--primary);
    border-radius: 0.25rem;
    position: relative;
    z-index: 1;
  }
  #hero-2041 .cs-topper:before {
    content: "";
    width: 100%;
    height: 100%;
    background: var(--primary);
    opacity: 0.1;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
  }
  #hero-2041 .cs-title {
    /* 39px - 80px */
    font-size: clamp(2.4375rem, 6vw, 5rem);
    max-width: 20ch;
    background: linear-gradient(to right, #3498db, #6dd5fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
  #hero-2041 .cs-text {
    max-width: 35.25rem;
    margin-bottom: 2rem;
    color: var(--bodyTextColorWhite);
  }
  #hero-2041 .cs-button-solid {
    font-size: 1rem;
    font-weight: 700;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-align: center;
    text-decoration: none;
    max-width: 11.25rem;
    margin: 0;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    padding: 0 2rem;
    background-color: var(--primary);
    overflow: hidden;
    color: var(--bodyTextColorWhite);
    border: none;
    border-radius: 0.5rem;
    display: inline-block;
    position: relative;
    z-index: 1;
  }
  #hero-2041 .cs-button-solid:before {
    content: "";
    width: 0%;
    height: 100%;
    background: #1a1a1a;
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #hero-2041 .cs-button-solid:hover:before {
    width: 100%;
  }
  #hero-2041 .cs-form {
    width: 100%;
    margin: 0;
    padding: 1rem;
    background-color: #fff;
    box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.08);
    border-radius: 0.5rem;
    display: flex;
    flex-direction: column;
    /* 16px - 20px */
    gap: clamp(1rem, 2.3vw, 1.25rem);
  }
  #hero-2041 .cs-label {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.2em;
    color: var(--headerColor);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  #hero-2041 .cs-flex {
    display: inline-flex;
    justify-content: flex-start;
    gap: 0;
  }
  #hero-2041 .cs-color {
    color: #ff4747;
  }
  #hero-2041 .cs-input {
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.5em;
    padding: 1rem;
    background-color: #f7f7f7;
    color: #767676;
    border: none;
    border-radius: 0.5rem;
  }
  #hero-2041 .cs-submit {
    width: max-content;
    align-self: center;
  }
  #hero-2041 .cs-background {
    width: 100%;
    height: 66%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -2;
  }
  #hero-2041 .cs-background:before {
    /* Overlay */
    content: "";
    width: 100%;
    height: 100%;
    background: #000;
    /* prevents the cursor from interacting with it */
    pointer-events: none;
    opacity: 0.48;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }
  #hero-2041 .cs-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #hero-2041 .cs-form {
    flex-direction: row;
    align-items: flex-end;
  }
  #hero-2041 .cs-submit {
    /* causes the submit button to grow to it's "auto" width and shrinks the inputs. this makes everything fit within cs-form */
    flex: 1 0 auto;
    align-self: flex-end;
  }
  #hero-2041 .cs-label {
    width: 100%;
    min-width: 0;
  }
  #hero-2041 .cs-background {
    height: 84%;
  }
}
/* Large Desktop - Parallax Effect - 1600px */
@media only screen and (min-width: 100rem) {
  #hero-2041 .cs-background {
    background: url("/assets/images/hero.webp");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
  }
  #hero-2041 .cs-background img {
    display: none;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #hero-2041 .cs-form {
    background-color: var(--medium);
  }
  body.dark-mode #hero-2041 .cs-label {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #hero-2041 .cs-input {
    background-color: var(--accent);
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #hero-2041 .cs-input::placeholder {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #hero-2041 .cs-background:before {
    opacity: 0.8;
  }
}

/*# sourceMappingURL=critical.css.map */
