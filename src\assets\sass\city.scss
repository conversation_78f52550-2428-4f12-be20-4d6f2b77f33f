.business-listings {
    padding: 4rem 0;
    background-color: #f4f7f6;
    min-height: 100vh;
}

.business-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;

    @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
        grid-template-columns: 1fr;
    }
}

.business-card {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease;
    border: 1px solid #eaeaea;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    height: 100%;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    }

    .business-image {
        width: 100%;
        height: 200px;
        overflow: hidden;
        border-radius: 6px;
        margin-bottom: 0.5rem;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        &:hover img {
            transform: scale(1.05);
        }
    }

    h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #333;
        margin: 0;
        line-height: 1.4;
    }

    .rating {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #f39c12;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;

        .review-count {
            color: var(--primary);
            font-size: 0.9rem;
        }
    }

    .address {
        color: #333;
        font-size: 0.95rem;
        line-height: 1.5;
        margin: 0;
        margin-bottom: .25rem;
    }

    .phone {
        color: #333;
        font-size: 1rem;
        font-weight: 500;
        margin: 0;
        margin-bottom: 0.5rem;
    }

    .view-website-btn {
        display: inline-block;
        background-color: var(--primary);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 5px;
        text-decoration: none;
        font-weight: 500;
        text-align: center;
        transition: background-color 0.3s ease;
        margin-top: auto;

        &:hover {
            background-color: #2980b9;
        }
    }
}

// Responsive adjustments
@media (max-width: 768px) {
    .business-listings {
        padding: 1rem;
    }

    .business-card {
        padding: 1.25rem;
        gap: 1.25rem;

        h3 {
            font-size: 1.2rem;
        }

        .business-image {
            height: 180px;
        }

        .view-website-btn {
            width: 100%;
            text-align: center;
        }
    }
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 3rem;
    padding: 1rem;
    flex-wrap: wrap;

    .page-link {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 40px;
        height: 40px;
        padding: 0 1rem;
        background-color: #ffffff;
        border: 1px solid #eaeaea;
        border-radius: 5px;
        color: #333;
        text-decoration: none;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
            background-color: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        &.active {
            background-color: #3498db;
            border-color: #3498db;
            color: white;
        }
    }
}

// Responsive adjustments for pagination
@media (max-width: 768px) {
    .pagination {
        gap: 0.25rem;
        margin-top: 2rem;

        .page-link {
            min-width: 35px;
            height: 35px;
            padding: 0 0.75rem;
            font-size: 0.9rem;
        }
    }
} 